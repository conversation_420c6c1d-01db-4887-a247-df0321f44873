import React from 'react';
import Card from '../../components/ui/Card';
import Button from '../../components/ui/Button';

function CreateExamSessionPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Create Exam Session</h1>
        <p className="text-gray-600">Set up a new exam monitoring session</p>
      </div>

      <Card>
        <Card.Header>
          <Card.Title>Session Configuration</Card.Title>
        </Card.Header>
        <Card.Content>
          <p className="text-gray-500">Exam session creation form will be implemented here.</p>
          <div className="mt-4">
            <Button variant="primary">Create Session</Button>
          </div>
        </Card.Content>
      </Card>
    </div>
  );
}

export default CreateExamSessionPage;
