{"name": "workbox-webpack-plugin", "version": "6.6.0", "description": "A plugin for your Webpack build process, helping you generate a manifest of local files that workbox-sw should precache.", "keywords": ["workbox", "workboxjs", "webpack", "service worker", "caching", "fetch requests", "offline", "file manifest"], "workbox": {"packageType": "node_ts"}, "main": "build/index.js", "types": "build/index.d.ts", "engines": {"node": ">=10.0.0"}, "dependencies": {"fast-json-stable-stringify": "^2.1.0", "pretty-bytes": "^5.4.1", "upath": "^1.2.0", "webpack-sources": "^1.4.3", "workbox-build": "6.6.0"}, "peerDependencies": {"webpack": "^4.4.0 || ^5.9.0"}, "devDependencies": {"@types/node": "^18.15.11", "@types/webpack": "^5.28.1"}, "author": "Google's Web DevRel Team", "license": "MIT", "repository": "googlechrome/workbox", "bugs": "https://github.com/GoogleChrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}