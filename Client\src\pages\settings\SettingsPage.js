import React from 'react';
import Card from '../../components/ui/Card';
import { ThemeSelector } from '../../components/ui/ThemeToggle';

function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Settings</h1>
        <p className="text-gray-600 dark:text-gray-400">Configure system settings</p>
      </div>

      {/* Theme Settings */}
      <Card>
        <Card.Header>
          <Card.Title>Appearance</Card.Title>
        </Card.Header>
        <Card.Content>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Theme
              </label>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                Choose your preferred theme or let the system decide based on your device settings.
              </p>
              <ThemeSelector />
            </div>
          </div>
        </Card.Content>
      </Card>

      {/* Other Settings */}
      <Card>
        <Card.Header>
          <Card.Title>System Configuration</Card.Title>
        </Card.Header>
        <Card.Content>
          <p className="text-gray-500 dark:text-gray-400">Other settings will be displayed here.</p>
        </Card.Content>
      </Card>
    </div>
  );
}

export default SettingsPage;
