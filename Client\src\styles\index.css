@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables */
:root {
  --primary-color: #3b82f6;
  --primary-dark: #1d4ed8;
  --secondary-color: #64748b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --background-color: #f8fafc;
  --surface-color: #ffffff;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --border-color: #e2e8f0;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #60a5fa;
  --primary-dark: #3b82f6;
  --secondary-color: #94a3b8;
  --success-color: #34d399;
  --warning-color: #fbbf24;
  --error-color: #f87171;
  --background-color: #0f172a;
  --surface-color: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
}

/* Base styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background-color);
  color: var(--text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Custom component styles */
.btn {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-warning {
  @apply bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-outline {
  @apply border-2 bg-transparent hover:bg-gray-50;
}

.btn-outline-primary {
  @apply border-blue-600 text-blue-600 hover:bg-blue-50;
}

.input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent;
}

.input-error {
  @apply border-red-500 focus:ring-red-500;
}

.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200;
}

/* Loading spinner */
.spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Notification styles */
.notification {
  @apply fixed top-4 right-4 z-50 max-w-sm w-full bg-white border border-gray-200 rounded-lg shadow-lg;
}

.notification-success {
  @apply border-green-500 bg-green-50;
}

.notification-error {
  @apply border-red-500 bg-red-50;
}

.notification-warning {
  @apply border-yellow-500 bg-yellow-50;
}

.notification-info {
  @apply border-blue-500 bg-blue-50;
}

/* Table styles */
.table {
  @apply w-full border-collapse;
}

.table th {
  @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

/* Dark theme component overrides */
[data-theme="dark"] .card {
  @apply bg-slate-800 border-slate-700;
}

[data-theme="dark"] .card-header {
  @apply border-slate-700;
}

[data-theme="dark"] .card-footer {
  @apply border-slate-700;
}

[data-theme="dark"] .input {
  @apply bg-slate-800 border-slate-600 text-slate-100;
}

[data-theme="dark"] .input:focus {
  @apply border-blue-500 ring-blue-500;
}

[data-theme="dark"] .btn-outline {
  @apply hover:bg-slate-800;
}

[data-theme="dark"] .btn-outline-primary {
  @apply border-blue-400 text-blue-400 hover:bg-slate-800;
}

[data-theme="dark"] .notification {
  @apply bg-slate-800 border-slate-700;
}

[data-theme="dark"] .notification-success {
  @apply border-green-400 bg-green-900/20;
}

[data-theme="dark"] .notification-error {
  @apply border-red-400 bg-red-900/20;
}

[data-theme="dark"] .notification-warning {
  @apply border-yellow-400 bg-yellow-900/20;
}

[data-theme="dark"] .notification-info {
  @apply border-blue-400 bg-blue-900/20;
}

[data-theme="dark"] .table th {
  @apply text-slate-300 border-slate-700;
}

[data-theme="dark"] .table td {
  @apply text-slate-200 border-slate-700;
}

/* Custom scrollbar for dark theme */
[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--surface-color);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: #475569;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Theme transition animations */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Theme toggle button styles */
.theme-toggle {
  @apply relative inline-flex items-center justify-center w-10 h-10 rounded-lg transition-all duration-200;
  @apply hover:bg-gray-100 dark:hover:bg-slate-700;
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

[data-theme="dark"] .theme-toggle {
  @apply hover:bg-slate-700;
}

/* Loading animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.table-striped tbody tr:nth-child(even) {
  @apply bg-gray-50;
}

.table-hover tbody tr:hover {
  @apply bg-gray-50;
}

/* Sidebar styles */
.sidebar {
  @apply fixed left-0 top-0 h-full w-64 bg-white border-r border-gray-200 transform transition-transform duration-300 ease-in-out z-40;
}

.sidebar-hidden {
  @apply -translate-x-full;
}

.sidebar-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 z-30;
}

/* Mobile responsive utilities */
@media (max-width: 768px) {
  .sidebar {
    @apply w-full;
  }
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation utilities */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-in-right {
  animation: slideInRight 0.3s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Focus styles for accessibility */
.focus-visible:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}
