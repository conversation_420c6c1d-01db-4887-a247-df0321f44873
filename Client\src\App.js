import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './context/auth/AuthContext';
import { ThemeProvider } from './context/theme/ThemeContext';
import { NotificationProvider } from './context/notification/NotificationContext';

// Layout Components
import MainLayout from './layouts/MainLayout';
import AuthLayout from './layouts/AuthLayout';

// Page Components
import HomePage from './pages/home/<USER>';
import LoginPage from './pages/auth/LoginPage';
import SignupPage from './pages/auth/SignupPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import ExamSessionsPage from './pages/exam-sessions/ExamSessionsPage';
import ExamSessionDetailPage from './pages/exam-sessions/ExamSessionDetailPage';
import CreateExamSessionPage from './pages/exam-sessions/CreateExamSessionPage';
import IncidentsPage from './pages/incidents/IncidentsPage';
import IncidentDetailPage from './pages/incidents/IncidentDetailPage';
import UsersPage from './pages/users/UsersPage';
import UserDetailPage from './pages/users/UserDetailPage';
import SettingsPage from './pages/settings/SettingsPage';
import ProfilePage from './pages/profile/ProfilePage';
import NotFoundPage from './pages/error/NotFoundPage';

// Route Guards
import ProtectedRoute from './components/guards/ProtectedRoute';
import AdminRoute from './components/guards/AdminRoute';

function App() {
  return (
    <ThemeProvider>
      <NotificationProvider>
        <AuthProvider>
          <Router>
            <Routes>
              {/* Public Routes with Auth Layout */}
              <Route path="/auth" element={<AuthLayout />}>
                <Route path="login" element={<LoginPage />} />
                <Route path="signup" element={<SignupPage />} />
              </Route>

              {/* Public Home Route */}
              <Route path="/" element={<HomePage />} />

              {/* Protected Routes with Main Layout */}
              <Route path="/app" element={
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }>
                {/* Dashboard */}
                <Route index element={<DashboardPage />} />
                
                {/* Exam Sessions */}
                <Route path="exam-sessions" element={<ExamSessionsPage />} />
                <Route path="exam-sessions/create" element={<CreateExamSessionPage />} />
                <Route path="exam-sessions/:id" element={<ExamSessionDetailPage />} />
                
                {/* Incidents */}
                <Route path="incidents" element={<IncidentsPage />} />
                <Route path="incidents/:id" element={<IncidentDetailPage />} />
                
                {/* Profile */}
                <Route path="profile" element={<ProfilePage />} />
                
                {/* Settings */}
                <Route path="settings" element={<SettingsPage />} />
                
                {/* Admin Only Routes */}
                <Route path="users" element={
                  <AdminRoute>
                    <UsersPage />
                  </AdminRoute>
                } />
                <Route path="users/:id" element={
                  <AdminRoute>
                    <UserDetailPage />
                  </AdminRoute>
                } />
              </Route>

              {/* 404 Page */}
              <Route path="*" element={<NotFoundPage />} />
            </Routes>
          </Router>
        </AuthProvider>
      </NotificationProvider>
    </ThemeProvider>
  );
}

export default App;
