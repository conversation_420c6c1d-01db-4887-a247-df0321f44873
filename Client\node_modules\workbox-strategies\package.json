{"name": "workbox-strategies", "version": "6.6.0", "license": "MIT", "author": "Google's Web DevRel Team", "description": "A service worker helper library implementing common caching strategies.", "repository": "googlechrome/workbox", "bugs": "https://github.com/googlechrome/workbox/issues", "homepage": "https://github.com/GoogleChrome/workbox", "keywords": ["workbox", "workboxjs", "service worker", "sw", "router", "routing"], "workbox": {"browserNamespace": "workbox.strategies", "packageType": "sw"}, "main": "index.js", "module": "index.mjs", "types": "index.d.ts", "dependencies": {"workbox-core": "6.6.0"}, "gitHead": "252644491d9bb5a67518935ede6df530107c9475"}